<script setup>
import { ref, onMounted } from 'vue'
import { useUsers } from '@/composables/useUsers'
import { useFirebase } from '@/composables/useFirebase'
import { collection, getDocs } from 'firebase/firestore'

const { getUser, updateUserPreferences } = useUsers()
const { db } = useFirebase()
const selectedRole = ref(null)

const users = ref([])
const selectedUser = ref(null)
const isLoading = ref(false)
const error = ref(null)
const success = ref(null)

const availableRoles = [
  { value: 'admin', label: 'Administrator' },
  { value: 'artist', label: 'Artist' },
  { value: 'bandLeader', label: 'Band Leader' }
]

async function loadUsers () {
  try {
    isLoading.value = true
    error.value = null

    const usersRef = collection(db, 'users')
    const querySnapshot = await getDocs(usersRef)
    users.value = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))
  } catch (err) {
    console.error('Error loading users:', err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}

async function handleUserSelect (userId) {
  if (!userId) return

  try {
    isLoading.value = true
    error.value = null
    success.value = null
    selectedUser.value = await getUser(userId)
  } catch (err) {
    console.error('Error selecting user:', err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}

async function addRole (roleName) {
  if (!selectedUser.value || !roleName) return

  try {
    isLoading.value = true
    error.value = null
    success.value = null

    // Check if role already exists
    const hasRole = selectedUser.value.roles.some(role =>
      Object.keys(role)[0] === roleName
    )

    if (hasRole) {
      error.value = `User already has ${roleName} role`
      return
    }

    // Add new role
    const newRoles = [...(selectedUser.value.roles || [])]
    newRoles.push({ [roleName]: {} })

    await updateUserPreferences(selectedUser.value.id, {
      roles: newRoles
    })

    // Refresh user data
    selectedUser.value = await getUser(selectedUser.value.id)
    success.value = `Added ${roleName} role successfully`
    selectedRole.value = null
  } catch (err) {
    console.error('Error adding role:', err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}

async function removeRole (roleName) {
  if (!selectedUser.value || !roleName) return

  try {
    isLoading.value = true
    error.value = null
    success.value = null

    // Remove role
    const newRoles = (selectedUser.value.roles || []).filter(role =>
      Object.keys(role)[0] !== roleName
    )

    await updateUserPreferences(selectedUser.value.id, {
      roles: newRoles
    })

    // Refresh user data
    selectedUser.value = await getUser(selectedUser.value.id)
    success.value = `Removed ${roleName} role successfully`
  } catch (err) {
    console.error('Error removing role:', err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}

onMounted(loadUsers)
</script>

<template>
  <h1>Manage User Roles</h1>

  <div v-if="error" class="error-message" role="alert">
    {{ error }}
  </div>

  <div v-if="success" class="success-message" role="alert">
    {{ success }}
  </div>

  <div v-if="isLoading && !selectedUser" class="loading">
    Loading users...
  </div>

  <div v-else class="user-selection">
    <BaseDropdown :modelValue="selectedUser?.id" :options="users.map(user => ({
      value: user.id,
      label: `${user.firstName} ${user.lastName} (${user.email})`
    }))" label="Select User" @update:modelValue="handleUserSelect" />
  </div>

  <div v-if="selectedUser" class="role-management">
    <h2>{{ selectedUser.fullName }}'s Roles</h2>

    <div v-if="isLoading" class="loading">
      Updating roles...
    </div>

    <div v-else>
      <div class="current-roles">
        <h3>Current Roles</h3>
        <div v-if="selectedUser.roles?.length" class="role-list">
          <div v-for="role in selectedUser.roles" :key="Object.keys(role)[0]" class="role-item">
            <span class="role-name">
              {{ Object.keys(role)[0] }}
            </span>
            <BaseButton variant="danger" size="small" @click="removeRole(Object.keys(role)[0])" :disabled="isLoading">
              Remove
            </BaseButton>
          </div>
        </div>
        <p v-else class="no-roles">
          No roles assigned
        </p>
      </div>

      <div class="add-role">
        <h3>Add Role</h3>
        <div class="role-actions">
          <BaseDropdown v-model="selectedRole" :options="availableRoles" label="Select Role" />
          <BaseButton @click="addRole(selectedRole)" :disabled="isLoading || !selectedRole">
            Add Role
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.error-message,
.success-message {
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.error-message {
  background: var(--color-error-soft);
  color: var(--color-error);
}

.success-message {
  background: var(--color-success-soft);
  color: var(--color-success);
}

.loading {
  text-align: center;
  color: var(--color-text-light);
  padding: 1rem;
}

.user-selection {
  margin-bottom: 2rem;
}

.role-management {
  background: var(--color-background-soft);
  padding: 1.5rem;
  border-radius: 0.5rem;
}

.current-roles,
.add-role {
  margin-top: 1.5rem;
}

h3 {
  font-size: var(--step-0);
  margin-bottom: 1rem;
}

.role-list {
  display: grid;
  gap: 0.75rem;
}

.role-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: var(--color-background-mute);
  border-radius: 0.5rem;
}

.role-name {
  font-weight: 500;
}

.role-actions {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.no-roles {
  color: var(--color-text-light);
  font-style: italic;
}
</style>
