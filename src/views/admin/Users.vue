<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { doc, updateDoc } from 'firebase/firestore'
import { getAuth } from 'firebase/auth'
import { vAutoAnimate } from '@formkit/auto-animate/vue'
import { useDebounceFn } from '@vueuse/core'
import { useFirebase } from '@/composables/useFirebase'
import { useUsers } from '@/composables/useUsers.ts'
import { User } from '@/models/User.ts'
import type { RoleType, UserRole } from '@/types/models'
import type { DocumentReference } from 'firebase/firestore'
import UserCard from '@/components/user/UserCard.vue'

const { db } = useFirebase()
const auth = getAuth()

const success = ref<string | null>(null)

const {
  users,
  loading: isLoading,
  subscribeToUsers,
  cleanup: cleanupUsers
} = useUsers()

// Add local error ref that can be string or null
const localError = ref<string | null>(null)

const editMode = ref<boolean>(false)
const currentUser = ref<any | null>(null)

// Form data
const editedUser = ref<Partial<User>>({
  id: '',
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  roles: []
})

const selectedRole = ref<RoleType | null>(null)

const availableRoles: { value: RoleType; label: string }[] = [
  { value: 'admin', label: 'Administrator' },
  { value: 'artist', label: 'Artist' },
  { value: 'bandLeader', label: 'Band Leader' }
]

// Debounced update function
const debouncedUpdateUser = useDebounceFn(async (userData: Partial<User>) => {
  try {
    if (!userData.id) {
      console.error('Cannot update user: missing id')
      return
    }

    const userRef: DocumentReference = doc(db, 'users', userData.id)
    await updateDoc(userRef, {
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      phone: userData.phone
    })
  } catch (error) {
    console.error('Error updating user:', error)
  }
}, 500)

function cancelEditing(): void {
  editMode.value = false
  editedUser.value = {
    id: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    roles: []
  }
  localError.value = null
  success.value = null
}

async function saveUser(): Promise<void> {
  if (!editedUser.value.id) return

  try {
    isLoading.value = true
    localError.value = null
    success.value = null

    const userRef = doc(db, 'users', editedUser.value.id)
    await updateDoc(userRef, {
      firstName: editedUser.value.firstName,
      lastName: editedUser.value.lastName,
      phone: editedUser.value.phone,
      roles: editedUser.value.roles
    })

    editMode.value = false
  } catch (err: unknown) {
    console.error('Error updating user:', err)
    localError.value = `Failed to update user: ${err instanceof Error ? err.message : 'Unknown error'}`
  } finally {
    isLoading.value = false
  }
}

function addRole(role: RoleType) {
  if (!editedUser.value.roles) {
    editedUser.value.roles = []
  }

  const roleObj: UserRole = { [role]: {} }
  editedUser.value.roles.push(roleObj)
  selectedRole.value = null
}

function removeRole(roleName: RoleType) {
  if (editedUser.value.roles) {
    editedUser.value.roles = editedUser.value.roles.filter(
      role => Object.keys(role)[0] !== roleName
    )
  }
}

function formatRoleName(roleName: RoleType | null): string {
  switch (roleName?.toLowerCase()) {
    case 'artist':
      return 'Artist'
    case 'bandleader':
      return 'Band Leader'
    case 'admin':
      return 'Administrator'
    default:
      return roleName ? roleName.charAt(0).toUpperCase() + roleName.slice(1) : ''
  }
}

// Safe handler for adding roles
function handleAddRole(): void {
  if (selectedRole.value) {
    addRole(selectedRole.value)
  }
}

onMounted(() => {
  subscribeToUsers()
  currentUser.value = auth.currentUser
})

onUnmounted(() => {
  cleanupUsers()
})
</script>

<template>
  <BaseSection>
    <template #header>
      <h1>Manage Users</h1>
      <p class="subtitle">View and edit user details, roles, and permissions</p>
    </template>

    <div v-if="localError" class="error-message" role="alert">
      {{ localError }}
    </div>

    <div v-if="success" class="success-message" role="alert">
      {{ success }}
    </div>

    <div v-if="isLoading" class="loading">
      Loading users...
    </div>

    <div v-else>
      <!-- Edit Mode -->
      <div v-if="editMode" class="edit-form">
        <form @submit.prevent="saveUser">
          <div class="form-row">
            <BaseInput v-model="editedUser.firstName" label="First Name" required
              @input="debouncedUpdateUser(editedUser)" />
            <BaseInput v-model="editedUser.lastName" label="Last Name" required
              @input="debouncedUpdateUser(editedUser)" />
          </div>

          <div class="form-row">
            <BaseInput v-model="editedUser.email" label="Email" type="email" disabled />
            <BaseInput v-model="editedUser.phone" label="Phone" type="tel" @input="debouncedUpdateUser(editedUser)" />
          </div>

          <div class="roles-section">
            <h3>User Roles</h3>
            <div class="current-roles">
              <div v-for="role in editedUser.roles" :key="Object.keys(role)[0]" class="role-tag">
                <span class="role-name">
                  {{ formatRoleName(Object.keys(role)[0] as RoleType) }}
                </span>
                <BaseButton purpose="danger" size="compact" @click="removeRole(Object.keys(role)[0] as RoleType)">
                  Remove
                </BaseButton>
              </div>
            </div>

            <div class="add-role">
              <BaseDropdown v-model="selectedRole" :options="availableRoles" placeholder="Select a role" />
              <BaseButton type="button" purpose="primary" @click="handleAddRole" :disabled="!selectedRole">
                Add Role
              </BaseButton>
            </div>
          </div>

          <div class="form-actions">
            <BaseButton type="submit" purpose="primary" :loading="isLoading">
              Save Changes
            </BaseButton>
            <BaseButton type="button" purpose="secondary" @click="cancelEditing" :disabled="isLoading">
              Cancel
            </BaseButton>
          </div>
        </form>
      </div>

      <!-- User List -->
      <div v-else v-auto-animate class="user-list">
        <UserCard v-for="user in users" :key="user.id" :user="user" />
      </div>
    </div>
  </BaseSection>
</template>

<style scoped>
.users-page {
  width: 100%;
}

.subtitle {
  color: var(--color-text-muted);
  margin-top: var(--space-xs);
}

.user-list {
  display: grid;
  gap: var(--space-s);
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.loading {
  text-align: center;
  padding: var(--space-m);
  color: var(--color-text-muted);
}

.error-message {
  color: var(--color-danger);
  margin-bottom: var(--space-m);
}

.success-message {
  color: var(--color-success);
  margin-bottom: var(--space-m);
}

.edit-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-s);
  margin-bottom: var(--space-m);
}

.roles-section {
  margin-top: var(--space-l);
}

.current-roles {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin: var(--space-s) 0;
}

.role-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-2xs) var(--space-xs);
  background-color: var(--color-surface);
  border-radius: var(--radius-sm);
}

.role-name {
  color: var(--color-text);
}

.add-role {
  display: flex;
  gap: var(--space-xs);
  margin-top: var(--space-s);
}

.form-actions {
  display: flex;
  gap: var(--space-s);
  margin-top: var(--space-l);
}

@media (max-width: 500px) {
  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
  }
}
</style>
