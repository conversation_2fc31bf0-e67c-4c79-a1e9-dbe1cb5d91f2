<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUsers } from '@/composables/useUsers'
import { useFirebase } from '@/composables/useFirebase'
import type { User } from '@/models/User'

const route = useRoute()
const router = useRouter()
const { getUser } = useUsers()
const { auth } = useFirebase()

const user = ref<User | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)

onMounted(async () => {
  try {
    const userData = await getUser(route.params.id as string)
    if (!userData) throw new Error('User not found')
    user.value = userData
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
  } finally {
    isLoading.value = false
  }
})

function goBack() {
  router.back()
}
</script>

<template>
  <BaseSection>
    <template #header>
      <div class="header">
        <h1>User Details</h1>
        <BaseButton @click="goBack" variant="outline">Back</BaseButton>
      </div>
    </template>

    <div v-if="isLoading" class="loading">Loading user details...</div>

    <div v-else-if="error" class="error">{{ error }}</div>

    <template v-else-if="user">
      <BaseCard>
        <div class="user-details">
          <div class="detail-group">
            <h2>{{ user.fullName }}</h2>
            <div class="user-meta">
              <BaseBadge v-if="auth.currentUser?.email === user.email" variant="solid" purpose="primary" size="xs">
                This is you
              </BaseBadge>
              <BaseBadge v-if="user.isSubscribed" variant="solid" purpose="info" size="xs">
                Subscriber
              </BaseBadge>
            </div>
          </div>

          <div class="detail-group">
            <label>Email</label>
            <span>{{ user.email }}</span>
          </div>

          <div class="detail-group">
            <label>Roles</label>
            <div class="roles-list">
              <BaseBadge v-for="role in user.roles" :key="Object.keys(role)[0]" variant="solid" purpose="primary"
                size="xs">
                {{ Object.keys(role)[0] }}
              </BaseBadge>
            </div>
          </div>

          <div v-if="user.phone" class="detail-group">
            <label>Phone</label>
            <span>{{ user.phone }}</span>
          </div>

          <div class="actions">
            <RouterLink v-if="auth.currentUser?.email === user.email" :to="`/admin/users/${user.id}/edit`"
              class="button-link button-link--solid button-link--primary button-link--rounded">
              Edit Profile
            </RouterLink>
          </div>
        </div>
      </BaseCard>
    </template>
  </BaseSection>
</template>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-m);
}

.user-details {
  display: grid;
  gap: var(--space-m);
}

.detail-group {
  display: grid;
  gap: var(--space-xs);
}

.detail-group label {
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.user-meta {
  display: flex;
  gap: var(--space-xs);
  align-items: center;
}

.roles-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.actions {
  margin-top: var(--space-l);
  padding-top: var(--space-m);
  border-top: 1px solid var(--color-border);
}

.loading,
.error {
  text-align: center;
  padding: var(--space-m);
  color: var(--color-text-muted);
}

.error {
  color: var(--color-danger);
}
</style>
