<script setup lang="ts">
import { ref } from 'vue'
import OptionSelect from '@/components/base/OptionSelect.vue'
import { ChevronLeft, ChevronRight, ListFilter } from 'lucide-vue-next'
import BaseCalendar from '@/components/calendar/BaseCalendar.vue'

const scopes = [
  { label: 'Week', value: 'week' },
  { label: 'Month', value: 'month' },
  { label: 'Year', value: 'year' }
]

const legends = [
  {
    label: 'Gigs',
    color: '#3F52B5'
  },
  {
    label: 'Tentative Dates',
    color: '#25F2F2'
  },
  {
    label: 'Rehearsals',
    color: '#05D222'
  },
  {
    label: 'Artist Unavailable',
    color: '#F56240'
  },
  {
    label: 'Meetings',
    color: '#F5F232'
  }
]

const dateData = Array.from({ length: 35 }, (_, i) => {
  const date = new Date('2025-12-28')
  date.setDate(date.getDate() + i)
  return {
    id: date.toISOString(),
    day: date.toLocaleDateString('en-GB', {
      day: 'numeric'
    }),

    month: date.toLocaleDateString('en-GB', {
      month: 'short'
    }),

    year: date.toLocaleDateString('en-GB', {
      year: '2-digit'
    })
  }
})

const selectedScope = ref(scopes[1])

function handleScopeChange(scope: string) {
  const newScope = scopes.find((s, i) => {
    return s.value === scope
  })

  if (newScope) {
    selectedScope.value = newScope
  }
}

function notThisYear(year: string) {
  return year !== new Date().toLocaleDateString('en-GB', {
    year: '2-digit'
  })
}
</script>

<template>
  <BaseSection>
    <template #header>
      <div class="section-header">
        <h2>Entertainment Calendar</h2>
        <OptionSelect :options="scopes" @change="handleScopeChange" :value="selectedScope.value" />
      </div>
    </template>
    <div class="navigation">
      <div class="current-date-selection">
        <ChevronLeft class="icon" /> {{ new Date().toLocaleDateString('en-GB', {
          month: 'long',
          year: 'numeric'
        }) }}
        <ChevronRight class="icon" />
        <BaseButton purpose="primary" variant="outline" :shadow="false">Today</BaseButton>
      </div>

      <div class="filter">
        <ListFilter class="icon" />
      </div>
    </div>
    <div class="calendar">
      <div class="weekdays">
        <div v-for="day in ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']" :key="day" class="weekday">
          {{ day }}
        </div>
      </div>
      <div class="calendar-grid">
        <div v-for="date in dateData" :key="date.id" class="date">
          {{ date.day }} <span>{{ date.month }}<span v-if="notThisYear(date.year)">, {{ date.year
          }}</span></span>
        </div>
      </div>
    </div>
    <div class="legends">
      <div v-for="legend in legends" :key="legend.label" class="legend">
        <div class="legend-color" :style="{ backgroundColor: legend.color }"></div>
        <div class="legend-label">{{ legend.label }}</div>
      </div>
    </div>
  </BaseSection>
</template>

<style scoped>
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-date-selection {
  display: flex;
  align-items: center;
  gap: .5em;
}

.icon {
  cursor: pointer;
}

.legends {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5em;
  margin-top: 1rem;
  font-size: var(--step--2);

  .legend {
    display: flex;
    align-items: center;
    gap: 0.3rem;
  }

  .legend-color {
    width: .75em;
    aspect-ratio: 1;
    border-radius: 50%;

    margin-right: 0.5rem;
  }
}

.calendar {
  .weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);

    .weekday {
      padding: 0.5rem;
      text-align: center;
      font-weight: 500;
      font-size: 0.875rem;
    }
  }

  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: .2em;
    line-height: 1;

    .date {
      padding: .5em .5em;
      aspect-ratio: 4 /3;
      border: 1px solid var(--border-color);
      background-color: var(--color-background-soft);

      span {
        font-size: var(--step--2);
        color: var(--color-text-muted);
      }
    }
  }
}
</style>
