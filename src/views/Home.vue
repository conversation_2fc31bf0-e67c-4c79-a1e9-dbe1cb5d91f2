<script setup lang="ts">
import { ref } from 'vue'
import { useFirebase } from '../composables/useFirebase'
import { Building, Building2, Calendar, CalendarDays, CalendarPlus, Eye, CheckSquare, Music, Users, ListCheck, BadgePoundSterling, LayoutGrid, LayoutList } from 'lucide-vue-next'
import TaskNotificationDot from '@/components/tasks/TaskNotificationDot.vue'
import { useLocalStorage } from '@vueuse/core'

const { currentUser, isLoading } = useFirebase()

const activeTab = ref('tasks')
const viewMode = useLocalStorage('home-view-mode', 'tabs') // 'tabs' or 'expanded'

function toggleViewMode() {
  viewMode.value = viewMode.value === 'tabs' ? 'expanded' : 'tabs'
}
</script>

<template>
  <p v-if="isLoading">Loading...</p>
  <div v-else>
    <!-- Public View -->
    <div v-if="!currentUser" class="public-view">
      <p>Please log in to access the admin panel.</p>
      <RouterLink to="/login" class="login-button">Login</RouterLink>
    </div>

    <!-- Admin View -->
    <div v-else class="admin-view">
      <!-- Tabbable menu system -->
      <BaseSection class="tabbable" title="Shortcuts">
        <template #header>
          <div class="tabbable-header">
            <h2>Shortcuts</h2>

            <BaseButton variant="outline" purpose="secondary" size="compact" @click="toggleViewMode"
              :title="`View as ${viewMode === 'tabs' ? 'grid' : 'tabs'}`">
              <LayoutGrid v-if="viewMode === 'tabs'" class="icon" />
              <LayoutList v-else class="icon" />
            </BaseButton>
          </div>
        </template>

        <div class="tabblable__body" :class="{ 'expanded-mode': viewMode === 'expanded' }">
          <div class="tabbable__tabs">
            <div class="tabbable__tab" :class="{ isActive: activeTab === 'tasks' }" @click="activeTab = 'tasks'">
              Tasks&nbsp;
              <TaskNotificationDot />
            </div>
            <div class="tabbable__tab" :class="{ isActive: activeTab === 'events' }" @click="activeTab = 'events'">
              Events</div>
            <div class="tabbable__tab" :class="{ isActive: activeTab === 'venues' }" @click="activeTab = 'venues'">
              Venues</div>
            <div class="tabbable__tab" :class="{ isActive: activeTab === 'songs' }" @click="activeTab = 'songs'">Songs
            </div>
            <div class="tabbable__tab" :class="{ isActive: activeTab === 'acts' }" @click="activeTab = 'acts'">Acts
            </div>
            <div class="tabbable__tab" :class="{ isActive: activeTab === 'artists' }" @click="activeTab = 'artists'">
              Artists</div>
          </div>
          <div class="tabbable__content">
            <div :class="{ isActive: activeTab === 'tasks' || viewMode === 'expanded' }">
              <h3 v-if="viewMode === 'expanded'" class="tasks-header">Tasks
                <TaskNotificationDot />
              </h3>
              <div class="action-buttons">
                <RouterLink :to="{ name: 'tasks' }" class="action-button shadow primary">
                  <ListCheck class="icon" /> View All Tasks
                </RouterLink>
                <RouterLink :to="{ name: 'tasks.create' }" class="action-button shadow success">
                  <CheckSquare class="icon" /> New Task
                </RouterLink>
              </div>
            </div>
            <div :class="{ isActive: activeTab === 'events' || viewMode === 'expanded' }">
              <h3 v-if="viewMode === 'expanded'">Events</h3>
              <div class="action-buttons">
                <RouterLink :to="{ name: 'events.index' }" class="action-button shadow primary">
                  <CalendarDays class="icon" /> View All Events
                </RouterLink>
                <RouterLink :to="{ name: 'events.create' }" class="action-button shadow success">
                  <CalendarPlus class="icon" /> New Event
                </RouterLink>
                <RouterLink :to="{ name: 'events.payments' }" class="action-button shadow secondary">
                  <BadgePoundSterling class="icon" /> Manage Payments
                </RouterLink>
              </div>
            </div>
            <div :class="{ isActive: activeTab === 'venues' || viewMode === 'expanded' }">
              <h3 v-if="viewMode === 'expanded'">Venues</h3>
              <div class="action-buttons">
                <RouterLink :to="{ name: 'venues.index' }" class="action-button shadow primary">
                  <Building2 class="icon" /> View All Venues
                </RouterLink>
                <RouterLink :to="{ name: 'venues.create' }" class="action-button shadow success">
                  <Building class="icon" /> New Venue
                </RouterLink>
              </div>
            </div>
            <div :class="{ isActive: activeTab === 'songs' || viewMode === 'expanded' }">
              <h3 v-if="viewMode === 'expanded'">Songs</h3>
              <div class="action-buttons">
                <RouterLink :to="{ name: 'songs.index' }" class="action-button shadow primary">
                  <Music class="icon" /> View All Songs
                </RouterLink>
              </div>
            </div>
            <div :class="{ isActive: activeTab === 'acts' || viewMode === 'expanded' }">
              <h3 v-if="viewMode === 'expanded'">Acts</h3>
              <div class="action-buttons">
                <RouterLink :to="{ name: 'acts.index' }" class="action-button shadow primary">
                  <Music class="icon" /> View All Acts
                </RouterLink>
              </div>
            </div>
            <div :class="{ isActive: activeTab === 'artists' || viewMode === 'expanded' }">
              <h3 v-if="viewMode === 'expanded'">Artists</h3>
              <div class="action-buttons">
                <RouterLink :to="{ name: 'artists.index' }" class="action-button shadow primary">
                  <Users class="icon" /> View All Artists
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <div class="quick-nav">
            <RouterLink :to="{ name: 'calendar' }" class="action-button shadow secondary">
              <Calendar class="icon" /> Calendar
            </RouterLink>
            <RouterLink :to="{ name: 'dashboard' }" class="action-button shadow secondary">
              <Eye class="icon" /> Dashboard
            </RouterLink>
          </div>
        </template>
      </BaseSection>
    </div>
  </div>
</template>

<style scoped>
.tabbable {
  .tabbable__tabs {
    display: flex;
    align-items: end;
    gap: .3rem;
    margin-inline-start: 1rem;
    overflow-x: scroll;

    .tabbable__tab {
      display: flex;
      align-items: center;
      gap: .2em;
      padding: var(--space-3xs) var(--space-s);
      border-radius: 0.5em .5em 0 0;
      background-color: var(--color-bg-2);
      color: light-dark(#0008, #fff8);
      cursor: pointer;
      box-shadow: 0 -13px 5px -10px #0003 inset;

      &.isActive {
        color: var(--color-text);
        background-color: var(--color-bg-4);
        box-shadow: none;
      }
    }
  }

  .tabbable-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 1;

    h2 {
      margin: 0;
    }
  }

  .tabbable__content {
    padding: 1rem;
    border-radius: 0.5em;
    background-color: var(--color-bg-4);
    color: #fff;

    >* {
      display: none;
    }

    .isActive {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
    }
  }

  /* Expanded mode styles */
  .expanded-mode {
    .tabbable__tabs {
      display: none;
    }

    .tabbable__content {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(min(100%, 400px), 1fr));
      gap: var(--space-m);
      padding: var(--space-m);
      background-color: var(--color-bg-2);

      >* {
        display: block !important;
        background-color: var(--color-bg-3);
        border-radius: var(--radius);
        padding: var(--space-m);
        box-shadow: var(--shadow);
      }

      h3 {
        font-size: var(--step-1);
        margin-bottom: var(--space-s);
        color: var(--color-text);
        border-bottom: 1px solid var(--color-border);
        padding-bottom: var(--space-2xs);

        &.tasks-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }

      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-s);
      }

      .action-button {
        margin-bottom: 0;
        /* flex: 1 1 auto; */
        min-width: 150px;
        justify-content: center;
      }
    }
  }
}

.tabbable__content div {
  display: flex;
  gap: var(--space-xs);
}

.quick-nav {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: right;
}

.action-button {
  color: var(--white);
  padding: var(--space-3xs) var(--space-xs);
  display: flex;
  gap: 1ch;
  align-items: center;

  /* background-color: var(--color-primary); */
  &.primary {
    background-color: var(--color-primary);
  }

  &.success {
    background-color: var(--color-success);
  }

  &.secondary {
    background-color: var(--color-secondary);
  }
}

.admin-view {
  display: grid;
  gap: 1rem;

  h2 {
    margin-block: var(--space-gap-m);
  }
}

.dialog-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: grid;
  place-items: center;
  z-index: 1000;
}

/* Public view styles */
.public-view {
  text-align: center;
  padding: 2rem;

  h1 {
    font-size: var(--step-3);
    margin-bottom: 1rem;
  }

  p {
    color: var(--color-text-muted);
    margin-bottom: 2rem;
  }
}

.login-button {
  display: inline-block;
  padding: 0.75em 1.5em;
  border-radius: 0.5em;
  background-color: var(--color-accent);
  color: var(--color-background);
  text-decoration: none;
  transition: filter var(--transition-in);

  &:hover {
    filter: brightness(1.1);
  }
}
</style>
