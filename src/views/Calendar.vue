<script setup lang="ts">
import BaseCalendar from '../components/calendar/BaseCalendar.vue'
</script>

<template>
  <BaseSection title="Calendar">
    <BaseCalendar />
  </BaseSection>
</template>

<style scoped>
.calendar-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
}

.nav-button,
.toggle-button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-background);
  cursor: pointer;
  transition: all 0.02s ease;
}

.nav-button:hover,
.toggle-button:hover {
  background: var(--color-background-alt);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toggle-button {
  margin-left: auto;
}
</style>
