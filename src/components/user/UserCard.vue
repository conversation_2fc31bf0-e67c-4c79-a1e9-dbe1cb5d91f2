<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getAuth } from 'firebase/auth'
import { CheckIcon } from 'lucide-vue-next'
import type { User as FirebaseUser } from 'firebase/auth'
import type { User } from '@/models/User'

const auth = getAuth()
const currentUser = ref<FirebaseUser | null>(null)

defineProps<{
  user: User
}>()

onMounted(() => {
  currentUser.value = auth.currentUser
})

function formatRoleName(roleName: string): string {
  return roleName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}
</script>

<template>
  <BaseCard class="user-card" :class="{ 'current-user': currentUser?.email === user.email }">
    <div class="user-content">
      <div class="user-info">
        <div class="user-name">
          <span class="name-text">{{ user.fullName }}</span>
          <BaseBadge v-if="user.isSubscribed" variant="solid" purpose="info" size="xs" aria-label="Verified Subscriber">
            <CheckIcon :stroke-width="2.5" :size="10" />
          </BaseBadge>
          <BaseBadge v-if="currentUser?.email === user.email" variant="solid" purpose="primary" size="xs"
            aria-label="This is you">
            me
          </BaseBadge>
        </div>
        <div class="user-email">
          {{ user.email }}
        </div>
        <div class="user-roles">
          <BaseBadge v-for="role in user.roles" :key="Object.keys(role)[0]" variant="outline" purpose="primary"
            size="xs">
            {{ formatRoleName(Object.keys(role)[0]) }}
          </BaseBadge>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <div class="user-actions">
        <RouterLink v-if="currentUser?.email === user.email" :to="{ name: 'edit-user', params: { id: user.id } }"
          class="edit-link">
          Edit
        </RouterLink>
        <RouterLink :to="{ name: 'user-details', params: { id: user.id } }">
          View Details
        </RouterLink>
      </div>
    </div>
  </BaseCard>
</template>

<style scoped>
.user-card {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

.user-content {
  flex: 1;
}

.card-footer {
  border-top: 1px solid var(--color-border);
  padding-top: var(--space-s);
  margin-top: auto;
}

.user-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-xs);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 500;
  color: var(--color-heading);
  display: flex;
  align-items: center;
  margin-bottom: var(--space-xs);
  gap: var(--space-2xs);
}

.name-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  color: var(--color-text-muted);
  font-size: var(--step--1);
  margin-bottom: var(--space-xs);
}

.user-roles {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2xs);
}

.role-badge,
.verified-badge-wrapper,
.verified-badge,
.me-badge {
  display: none;
}

.user-actions a {
  font-size: var(--step--1);
  color: var(--color-brand);
  text-decoration: none;
  padding: var(--space-2xs) var(--space-s);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.edit-link {
  color: var(--color-text-muted);
}

.auth-details {
  font-size: var(--step--1);
  color: var(--color-text-muted);
  margin-top: var(--space-2xs);
  display: flex;
  flex-direction: column;
  gap: var(--space-3xs);
}

@media (max-width: 500px) {
  .user-actions {
    flex-direction: column;
    width: 100%;
    gap: var(--space-xs);
  }
}
</style>
