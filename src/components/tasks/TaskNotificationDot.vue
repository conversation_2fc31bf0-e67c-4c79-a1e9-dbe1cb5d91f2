<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useTasks } from '@/composables/useTasks'

const { urgentTasks, incompleteTasks, subscribeToTasks, cleanup: tasksCleanup } = useTasks()

// Add computed properties for task status
const taskStatus = computed(() => {
  const now = new Date()
  const threeDaysFromNow = new Date(now.getTime() + (3 * 24 * 60 * 60 * 1000))

  const hasOverdue = incompleteTasks.value.some(task =>
    task.dueDate && task.dueDate < now
  )

  const hasNearDue = !hasOverdue && incompleteTasks.value.some(task =>
    task.dueDate && task.dueDate > now && task.dueDate <= threeDaysFromNow
  )

  if (hasOverdue) return 'overdue'
  if (hasNearDue) return 'near-due'
  return incompleteTasks.value.length > 0 ? 'pending' : null
})

onMounted(() => {
  subscribeToTasks()
})

onUnmounted(() => {
  tasksCleanup()
})
</script>

<template>
  <sup class="notification-dot" :class="[
    `status-${taskStatus}`, { throb: urgentTasks.length }
  ]">
    {{ incompleteTasks.length }}
  </sup>
</template>

<style scoped>
.notification-dot {
  display: inline-grid;
  font-size: 0.75rem;
  font-weight: 700;
  line-height: 1;
  background-color: green;
  color: white;
  border-radius: 1em;
  box-shadow: var(--box-shadow-light);
  padding: 0.25em .35em;

  &.status-pending {
    background-color: var(--color-success, #22c55e);
  }

  &.status-near-due {
    background-color: var(--color-warning, #f59e0b);
  }

  &.status-overdue {
    background-color: var(--color-danger, #ef4444);
  }

  &.throb {
    animation: throb 1s infinite;
  }
}

@keyframes throb {
  0% {
    scale: 1;
    opacity: .6;
  }

  50% {
    scale: 1.3;
    opacity: 1;
  }

  100% {
    scale: 1;
    opacity: .6;
  }
}
</style>
