<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

type Option = {
  value: string
  label: string
  searchData?: string[]
}

type Props = {
  modelValue?: string | null
  options: Option[]
  placeholder?: string
  disabled?: boolean
  clearOnClickAway?: boolean // New prop to control click-away behavior
  label?: string
  required?: boolean
  clearable?: boolean // New prop
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: 'Search...',
  disabled: false,
  clearOnClickAway: false,
  label: '',
  required: false,
  clearable: true // Default to true
})

const emit = defineEmits<{
  'update:modelValue': [value: string | null]
  'change': [value: string | null]
}>()

const isOpen = ref(false)
const searchQuery = ref('')
const dropdownRef = ref<HTMLElement | null>(null)
const inputRef = ref<HTMLInputElement | null>(null)
const highlightedIndex = ref(-1)

const filteredOptions = computed(() => {
  if (!searchQuery.value) return props.options

  const query = searchQuery.value.toLowerCase()
  return props.options.filter(option => {
    const matchLabel = option.label.toLowerCase().includes(query)
    const matchSearchData = option.searchData?.some(data =>
      data?.toLowerCase().includes(query)
    )
    return matchLabel || matchSearchData
  })
})

const selectedOption = computed(() =>
  props.options.find(option => option.value === props.modelValue)
)

// Reset highlighted index when filtered options change
watch(filteredOptions, () => {
  highlightedIndex.value = -1
})

watch(() => props.modelValue, (newValue) => {
  const option = props.options.find(opt => opt.value === newValue)
  if (option) {
    searchQuery.value = option.label
  } else {
    searchQuery.value = ''
  }
}, { immediate: true })

watch(() => props.options, () => {
  if (props.modelValue) {
    const option = props.options.find(opt => opt.value === props.modelValue)
    if (option) {
      searchQuery.value = option.label
    }
  }
}, { immediate: true, deep: true })

function handleSelect(option: Option) {
  emit('update:modelValue', option.value)
  emit('change', option.value)
  searchQuery.value = option.label
  isOpen.value = false
  highlightedIndex.value = -1
}

function handleInput(event: Event) {
  const value = (event.target as HTMLInputElement).value
  searchQuery.value = value
  isOpen.value = true
}

function handleFocus() {
  isOpen.value = true
}

function handleClickOutside(event: MouseEvent) {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false

    if (props.clearOnClickAway && searchQuery.value !== selectedOption.value?.label) {
      // Clear selection if search query doesn't match selected option
      emit('update:modelValue', '')
      emit('change', '')
      searchQuery.value = ''
    } else {
      // Reset to selected option or empty
      searchQuery.value = selectedOption.value?.label || ''
    }

    highlightedIndex.value = -1
  }
}

function handleKeydown(event: KeyboardEvent) {
  if (props.disabled) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      if (!isOpen.value) {
        isOpen.value = true
      } else {
        highlightedIndex.value = Math.min(
          highlightedIndex.value + 1,
          filteredOptions.value.length - 1
        )
        scrollHighlightedIntoView()
      }
      break

    case 'ArrowUp':
      event.preventDefault()
      if (isOpen.value) {
        highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
        scrollHighlightedIntoView()
      }
      break

    case 'Enter':
      event.preventDefault()
      if (isOpen.value && highlightedIndex.value >= 0) {
        handleSelect(filteredOptions.value[highlightedIndex.value])
      }
      break

    case 'Escape':
      event.preventDefault()
      isOpen.value = false
      searchQuery.value = selectedOption.value?.label || ''
      highlightedIndex.value = -1
      inputRef.value?.blur()
      break

    case 'Tab':
      if (isOpen.value) {
        isOpen.value = false
        searchQuery.value = selectedOption.value?.label || ''
      }
      break
  }
}

function scrollHighlightedIntoView() {
  nextTick(() => {
    const highlighted = dropdownRef.value?.querySelector('.search-select__option--highlighted')
    if (highlighted) {
      highlighted.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth'
      })
    }
  })
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  if (selectedOption.value) {
    searchQuery.value = selectedOption.value.label
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const id = computed(() => `search-select-${Math.random().toString(36).substr(2, 9)}`)

// Add clear handler function
function handleClear(event: Event) {
  event.preventDefault()
  event.stopPropagation()
  emit('update:modelValue', null)
  emit('change', null)
  searchQuery.value = ''
  isOpen.value = false
}
</script>

<template>
  <div class="search-select-wrapper">
    <label v-if="label" :for="id" class="search-select__label">
      {{ label }}
      <span v-if="required" class="required-indicator">*</span>
    </label>

    <div class="search-select" ref="dropdownRef" role="combobox" :aria-expanded="isOpen" aria-haspopup="listbox">
      <div class="search-select__input-wrapper">
        <input :id="id" ref="inputRef" type="text" v-model="searchQuery" :placeholder="placeholder" :disabled="disabled"
          :aria-activedescendant="highlightedIndex >= 0 ? `option-${filteredOptions[highlightedIndex]?.value}` : undefined"
          class="search-select__input" @input="handleInput" @focus="handleFocus" @keydown="handleKeydown"
          aria-autocomplete="list">
        <button v-if="clearable && modelValue && !disabled" type="button" class="search-select__clear-button"
          @click="handleClear" tabindex="-1" aria-label="Clear selection">
          ×
        </button>
      </div>

      <div v-show="isOpen" class="search-select__dropdown" role="listbox">
        <div class="search-select__options">
          <button v-for="(option, index) in filteredOptions" :key="option.value" type="button"
            :id="`option-${option.value}`" class="search-select__option" role="option"
            :aria-selected="option.value === modelValue" :class="{
              'search-select__option--selected': option.value === modelValue,
              'search-select__option--highlighted': index === highlightedIndex
            }" @click="handleSelect(option)" @mouseover="highlightedIndex = index">
            {{ option.label }}
          </button>
          <div v-if="filteredOptions.length === 0" class="search-select__no-results" role="alert">
            No results found
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.search-select-wrapper {
  display: grid;
  gap: 0.5rem;
}

.search-select__label {
  font-size: var(--step--1);
  font-weight: 500;
  color: var(--color-text);
}

.required-indicator {
  color: var(--color-error);
  margin-left: 0.25rem;
}

.search-select {
  position: relative;
  width: 100%;
}

.search-select__input-wrapper {
  position: relative;
  width: 100%;
}

.search-select__input {
  width: 100%;
  padding: 0.5rem 1rem;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  font-size: inherit;
  transition: all 0.2s ease;
  padding-right: 2rem;
}

.search-select__input:hover:not(:disabled) {
  border-color: var(--color-border-hover);
}

.search-select__input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

.search-select__input:disabled {
  background-color: var(--color-background-alt);
  cursor: not-allowed;
  opacity: 0.7;
}

.search-select__clear-button {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 1.2em;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.2em;
  height: 1.2em;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.search-select__clear-button:hover {
  color: var(--color-text);
  background-color: var(--color-border);
}

.search-select__clear-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

.search-select__dropdown {
  position: absolute;
  top: calc(100% + 0.25rem);
  left: 0;
  right: 0;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-md);
  z-index: 50;
  max-height: 200px;
  overflow-y: auto;
}

.search-select__option {
  width: 100%;
  padding: 0.5rem 1rem;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text);
  transition: background-color 0.2s ease;
}

.search-select__option:hover {
  background-color: var(--color-background-alt);
}

.search-select__option--selected {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.search-select__option--highlighted {
  background-color: var(--color-background-alt);
}

.search-select__option--selected.search-select__option--highlighted {
  background-color: var(--color-primary-dark, #2563eb);
}

.search-select__no-results {
  padding: 0.5rem 1rem;
  color: var(--color-text-muted);
  text-align: center;
}
</style>
```

This component features:
- Search functionality with support for additional searchable data
- Keyboard navigation (TODO)
- Customizable placeholders
- Disabled state
- Proper click-outside handling
- Consistent styling with other base components

To use it in your venues selection:

```vue
<BaseSearchSelect v-model="selectedVenueId" :options="venues.value?.map(venue => ({
    value: venue.id,
    label: formatVenueName(venue),
    searchData: [
      venue.address?.town,
      venue.address?.county,
      venue.address?.postcode,
      venue.address?.address1,
      venue.address?.address2
    ].filter(Boolean)
  })) || []" placeholder="Select a venue" searchPlaceholder="Search venues..." />
```

Would you like me to add any additional features or make any adjustments to this component?
