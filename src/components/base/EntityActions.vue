<script setup lang="ts">
import { Eye, Pencil, Trash2 } from 'lucide-vue-next'

type Props = {
  icon?: boolean
  text?: boolean
  canView?: boolean
  canEdit?: boolean
  canDelete?: boolean
  isLoading?: boolean
  showStatusControls?: boolean
  status?: string
  isPast?: boolean
  size?: 'tiny' | 'compact' | 'default' | 'large'
}

withDefaults(defineProps<Props>(), {
  icon: false,
  text: false,
  canView: true,
  canEdit: true,
  canDelete: true,
  showStatusControls: false,
  isPast: false,
  size: 'default'
})

const emit = defineEmits(['view', 'edit', 'delete', 'status-change'])

const statusOptions = [
  { value: 'draft', label: 'Draft' },
  { value: 'published', label: 'Published' },
  { value: 'cancelled', label: 'Cancelled' },
  { value: 'postponed', label: 'Postponed' },
  { value: 'rescheduled', label: 'Rescheduled' },
  { value: 'save the date', label: 'Save the Date' }
]

const statusOption = (value: string) => {
  const option = statusOptions.find(option => option.value === value)
  return option ? option.label : 'Unknown'
}

function updateStatus(newValue: string) {
  emit('status-change', newValue)
}
</script>

<template>
  <div class="entity-actions" :class="`entity-actions--${size}`">
    <slot name="before" />

    <div v-if="showStatusControls && !isPast" class="status-controls">
      <BaseSelect :model-value="status" :options="statusOptions" :disabled="isLoading"
        @update:model-value="updateStatus" class="status-select" :size />
    </div>

    <div v-else-if="showStatusControls && isPast" class="status-controls-text">
      {{ statusOption(status || '') }}
    </div>

    <BaseButton v-if="canView" :disabled="isLoading" purpose="primary" @click="emit('view')" title="View Details" :size>
      <Eye v-if="icon || !text" class="icon" />
      <span v-if="text || !icon">View</span>
    </BaseButton>

    <BaseButton v-if="canEdit" :disabled="isLoading" purpose="secondary" @click="emit('edit')" title="Edit Event" :size>
      <Pencil v-if="icon || !text" class="icon" />
      <span v-if="text || !icon">Edit</span>
    </BaseButton>

    <BaseButton v-if="canDelete" purpose="danger" :disabled="isLoading" @click="emit('delete')" title="Delete Event"
      :size>
      <Trash2 v-if="icon || !text" class="icon" />
      <span v-if="text || !icon">Delete</span>
    </BaseButton>

    <slot name="after" />
  </div>
</template>

<style scoped>
.entity-actions {
  display: flex;
  gap: var(--space-3xs);
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
}

.status-controls {
  display: flex;
  gap: var(--space-3xs);
}

.status-controls-text {
  color: var(--color-text-muted);
  font-size: var(--step--1);
  background-color: var(--color-background-mute);
  padding: var(--space-3xs) var(--space-2xs);
  border-radius: var(--radius-sm);
}

.status-select {
  min-width: 140px;
}

.icon {
  width: 1em;
  height: 1em;
}
</style>
