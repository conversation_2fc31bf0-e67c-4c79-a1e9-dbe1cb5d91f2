<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useVenues } from '@/composables/useVenues'
import { useActs } from '@/composables/useActs'
import { Event, type EventNotes } from '@/models/Event'
import { useEventForm, MIN_DURATION } from '@/composables/useEventForm'
import ActSelector from '@/components/ActSelector.vue'
import BaseSearchSelect from '../base/BaseSearchSelect.vue'
import VenueForm from '@/views/venues/VenueForm.vue'
import { Timestamp } from 'firebase/firestore'
import { Venue } from '@/models/Venue'
import { useDateTime } from '@/composables/useDateTime'
import { useAgents } from '@/composables/useAgents'

type Props = {
  initialValues?: Partial<Event>
  isEditMode?: boolean
  date?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  isEditMode: false
})

const {
  formData,
  isValid,
  getFieldError,
  updateField
} = useEventForm(props.initialValues)

const emit = defineEmits<{ submit: [data: Partial<Event>] }>()

const isSubmitting = ref(false)
const submitError = ref<string | null>(null)

const { venues, subscribeToVenues } = useVenues()
const { acts, subscribeToActs } = useActs()


const showVenueModal = ref(false)

const { getNextAvailable8PM } = useDateTime()

// Subscribe to required data
subscribeToVenues()
subscribeToActs()

// Computed properties for form fields
const venueOptions = computed(() =>
  (venues.value ?? []).map(venue => ({
    value: venue.id,
    label: venue.name,
    searchData: [
      venue.address?.address1,
      venue.address?.address2,
      venue.address?.town,
      venue.address?.county,
      venue.address?.postcode
    ].filter(Boolean) // Remove null/undefined values
  }))
)

const statusOptions = [
  { label: 'Draft', value: 'draft' },
  { label: 'Published', value: 'published' },
  { label: 'Cancelled', value: 'cancelled' },
  { label: 'Postponed', value: 'postponed' },
  { label: 'Rescheduled', value: 'rescheduled' },
  { label: 'Save the Date', value: 'save the date' }
]

const ctaTypeOptions = [
  { label: 'Select CTA Type', value: null },
  { label: 'Facebook Event', value: 'fb' },
  { label: 'Ticket Link', value: 'ticket' }
]

const isCtaTypeSelected = ref(!!formData.value.cta?.type)

// Add this computed property
const selectedActs = computed({
  get: () => formData.value.acts ?? [],
  set: (value) => updateField('acts', value)
})

// Add computed properties for nested form fields
const ctaType = computed({
  get: () => formData.value.cta?.type ?? null,
  set: (value) => {
    const optionValue = ctaTypeOptions.find(option => option.label === value)?.value

    if (optionValue === null) {
      formData.value.cta = null
      isCtaTypeSelected.value = false
      return
    }

    if (value) {
      formData.value.cta = {
        ...formData.value.cta,
        type: value,
        text: formData.value.cta?.text ?? '',
        url: formData.value.cta?.url ?? '',
        price: formData.value.cta?.price ?? null
      }
      isCtaTypeSelected.value = true
    } else {
      formData.value.cta = null
      isCtaTypeSelected.value = false
    }
  }
})

// Add this helper function
const formatDateForInput = (date: Date | string | null): string => {
  if (!date) return ''

  try {
    const d = date instanceof Date ? date : new Date(date)
    if (isNaN(d.getTime())) return ''

    // Format as YYYY-MM-DDTHH:mm
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day}T${hours}:${minutes}`
  } catch (error) {
    console.error('Error formatting date:', error)
    return ''
  }
}

// Update the eventDateTime computed property
const eventDateTime = computed({
  get: () => {
    if (!formData.value.when) {
      const defaultDate = getNextAvailable8PM()
      // Set the default date immediately if when is null
      updateField('when', Timestamp.fromDate(defaultDate))
      return formatDateForInput(defaultDate)
    }

    return formatDateForInput(formData.value.when.toDate())
  },
  set: (value: string) => {
    if (!value) return  // Don't clear the date if value is empty
    try {
      const newDate = new Date(value)
      if (!isNaN(newDate.getTime())) {
        updateField('when', Timestamp.fromDate(newDate))
      }
    } catch (error) {
      console.error('Error parsing date:', error)
    }
  }
})

onMounted(() => {
  if (props.date) {
    // Set the eventDateTime value (20:00 hours default)
    let time
    if (props.date.includes('T')) {
      time = props.date.split('T')[1]
    } else {
      time = '20:00:00.000Z'
      if (new Date().getTimezoneOffset() === -60) {
        time = '19:00:00.000Z'
      }
    }

    eventDateTime.value = props.date.split('T')[0] + 'T' + time
  }
})

const minDateTime = computed(() => {
  const date = new Date()
  date.setHours(date.getHours() + 8)
  return formatDateForInput(date)
})

const handleSubmit = () => {
  submitError.value = null
  isSubmitting.value = true

  if (!isValid.value) {
    submitError.value = 'Please fix the form errors before submitting'
    return
  }

  emit('submit', formData.value as Partial<Event>)
}

const updateNotesField = (category: 'fee' | 'deposit' | 'agent', field: string, value: any) => {
  if (category === 'agent') {
    updateField('notes', {
      ...formData.value.notes,
      agent: value || null
    } as EventNotes)
    return
  }

  // Only fee and deposit need number transformation for amount
  updateField('notes', {
    ...formData.value.notes,
    [category]: {
      ...formData.value.notes?.[category],
      amount: field === 'amount' ? (value !== '' ? Number(value) : null) : formData.value.notes?.[category]?.amount,
      paid: formData.value.notes?.[category]?.paid ?? false,
      date: formData.value.notes?.[category]?.date ?? null
    }
  } as EventNotes)
}

const handleAddNewVenue = () => {
  showVenueModal.value = true
}

const handleVenueSubmit = async (formData: Partial<Venue>) => {
  try {
    const { createVenue } = useVenues()
    const venueId = await createVenue(formData)

    // Update the event form with the new venue
    updateField('venue', venueId)

    // Close the modal
    showVenueModal.value = false
  } catch (e) {
    console.error('Error creating venue:', e)
  }
}

const titlePlaceholder = computed(() => {
  const selectedActs = formData.value.acts?.map(actId => {
    const act = acts.value?.find(a => a.id === actId)
    return act ? act.displayName || act.name : 'Unknown Act'
  })
  const selectedVenue = venues.value?.find(v => v.id === formData.value.venue)

  let placeholder = ''

  if (selectedActs && selectedActs.length && selectedVenue) {
    placeholder = `${selectedActs.join('/')} @ ${selectedVenue.name}, ${selectedVenue.address?.town}`
  } else if (selectedActs && selectedActs.length) {
    placeholder = `${selectedActs.join('/')}`
  } else if (selectedVenue) {
    placeholder = `${selectedVenue.name}, ${selectedVenue.address?.town}`
  }

  return placeholder || 'Select acts and venue'
})

const computedId = computed(() => {
  const date = formData.value.when?.toDate().toISOString().split('T')[0] || ''
  const venue = venues.value?.find(v => v.id === formData.value.venue)
  const venueName = venue?.name || ''
  const town = (venue?.address.town && venueName.includes(venue?.address.town)) ? '' : venue?.address?.town || ''

  const acts = formData.value.acts?.join('-') || ''

  return `${date}-${venueName}-${town}-${acts}`
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
})

const displayId = computed(() => {
  if (props.isEditMode && props.initialValues?.id) {
    return props.initialValues.id
  }
  return computedId.value
})

const { agents, subscribeToAgents } = useAgents()

// Subscribe to agents data alongside other subscriptions
subscribeToAgents()

// Add a computed property for agent options with proper undefined handling
const agentOptions = computed(() => {
  if (!agents.value) return []  // Return empty array if agents is undefined

  return agents.value.map((agent: any) => ({
    value: agent.id,
    label: agent.name,
    searchData: [
      agent.email,
      agent.phone
    ].filter(Boolean)
  }))
})
</script>

<template>
  <form class="event-form" @submit.prevent>
    <!-- Basic Information -->
    <section class="form-section">
      <h2>Basic Information</h2>
      <small class="computed-id">{{ isEditMode ? 'Event ID:' : 'Generated ID:' }} {{ displayId }}</small>
      <BaseInput v-model="formData.title" label="Event Title" :error="getFieldError('title')"
        :placeholder="titlePlaceholder" />

      <BaseEditor v-model="formData.description" label="Description" :error="getFieldError('description')"
        placeholder="Enter description... (optional)" />

      <div class="flex">
        <BaseInput v-model="eventDateTime" type="datetime-local" label="Date & Time" :error="getFieldError('when')"
          :min="minDateTime" required />

        <BaseInput v-model="formData.duration" type="number" label="Duration (minutes)"
          :error="getFieldError('duration')" :min="MIN_DURATION" step="15" required />
      </div>

      <div class="venue-controls">
        <BaseSearchSelect v-model="formData.venue" :options="venueOptions" label="Venue" placeholder="Search venues..."
          :error="getFieldError('venue')" required />
        <BaseButton type="button" @click="handleAddNewVenue" size="tiny">Add New Venue</BaseButton>
      </div>

      <BaseDialog v-model="showVenueModal" title="Add New Venue">
        <VenueForm @submit="handleVenueSubmit" @close="showVenueModal = false" />
      </BaseDialog>

      <ActSelector v-model="selectedActs" :acts="acts as any" :error="getFieldError('acts')" required />
    </section>


    <label>
      <input type="checkbox" v-model="formData.isPrivate">
      Private Event
    </label>
    <label>
      <BaseSelect v-model="formData.status" :options="statusOptions" label="Status" :error="getFieldError('status')" />
    </label>


    <section class="form-section">
      <h2>Call to Action</h2>

      <BaseSelect v-model="ctaType" :options="ctaTypeOptions" label="CTA Type" :error="getFieldError('cta.type')" />
      <template v-if="formData.cta?.type && isCtaTypeSelected">
        <BaseInput v-model="formData.cta.text" label="Button Text" :error="getFieldError('cta.text')"
          placeholder="e.g., 'Get Tickets' or 'View Event'" />

        <BaseInput v-model="formData.cta.url" label="URL" type="url" :error="getFieldError('cta.url')"
          placeholder="https://" />

        <BaseInput v-if="formData.cta.type === 'ticket'" v-model="formData.cta.price" label="Ticket Price" type="text"
          :error="getFieldError('cta.price')" placeholder="e.g., '£10' or 'From £15'" />
      </template>
    </section>

    <section class="form-section">
      <h2>Payment & Notes</h2>

      <!-- Fee -->
      <div class="subsection">
        <h3>Fee</h3>
        <BaseInput :model-value="formData.notes?.fee?.amount"
          @update:model-value="(value: any) => updateNotesField('fee', 'amount', value)" type="number" label="Amount"
          :error="getFieldError('notes.fee.amount')" />
      </div>

      <!-- Deposit -->
      <div class="subsection">
        <h3>Deposit</h3>
        <BaseInput :model-value="formData.notes?.deposit?.amount"
          @update:model-value="(value: any) => updateNotesField('deposit', 'amount', value)" type="number"
          label="Amount" :error="getFieldError('notes.deposit.amount')" />
      </div>

      <!-- Agent -->
      <div class="subsection">
        <h3>Agent</h3>
        <BaseSearchSelect :model-value="formData.notes?.agent"
          @update:model-value="(value: any) => updateNotesField('agent', '', value)" :options="agentOptions"
          label="Agent" placeholder="Search agents..." :error="getFieldError('notes.agent')" />
      </div>
    </section>

    <!-- Form Actions -->
    <div class="form-actions">
      <p v-if="submitError" class="error-message">
        {{ submitError }}
      </p>
      <button type="button" @click="handleSubmit" :disabled="isSubmitting">
        {{ isSubmitting ? 'Submitting...' : 'Submit' }}
      </button>
    </div>
  </form>
</template>

<style scoped>
.event-form {
  min-width: 800px;
  margin: 0 auto;
}

.computed-id {
  display: block;
  margin-bottom: 1rem;
  font-size: var(--step--1);
  color: var(--color-text-muted);
  max-width: 60ch;
}

.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--color-background-soft);
  border-radius: 8px;
}

.form-section>*+* {
  margin-top: 1rem;
}

.form-section h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  color: var(--color-heading);
}

.subsection {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-border);
}

.subsection h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: var(--color-text);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-border);
}

.form-section-toggles {
  margin: 1rem 0;
  display: flex;
  gap: 1rem;
}

.venue-controls {
  display: flex;
  align-items: end;
  justify-content: space-between;
  gap: 1rem;

  &>:first-child {
    flex-basis: 100%;
  }
}

@media (max-width: 768px) {
  .event-form {
    min-width: 100%;
    /* On mobile, let it take full width of viewport */
  }
}

/* Modal-related styles removed as they're no longer needed */

.error-message {
  color: var(--color-error);
  margin-top: 1rem;
  font-weight: 500;
}
</style>
