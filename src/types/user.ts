import { Timestamp } from 'firebase/firestore'

export type AuthProvider = 'password' | 'google' | 'github' | 'facebook'

export interface TwoFactorAuthConfig {
  enabled: boolean
  method: 'authenticator' | 'sms' | 'email'
  verified: boolean
  backupCodes?: string[]
}

export interface UserActivity {
  id?: string
  timestamp: Timestamp
  action: string
  details?: Record<string, any>
  ip?: string
  userAgent?: string
}

export interface UserPreferences {
  isSubscribed: boolean
  viewAsGrid: boolean
  emailNotifications: {
    security: boolean
    marketing: boolean
    updates: boolean
  }
  theme: 'light' | 'dark' | 'system'
}

export interface RoleData {
  actId?: string
  artistId?: string
  permissions?: string[]
  grantedAt?: Timestamp
  grantedBy?: string
}

export type UserRole = {
  [K in 'admin' | 'artist' | 'bandLeader' | 'user']: RoleData
}

export interface UserData {
  id?: string
  email: string
  firstName: string
  lastName: string
  username: string
  prefs: UserPreferences
  tags: string[]
  phone?: string
  birthday?: Timestamp | null
  address?: string
  isFrightened?: boolean
  roles?: UserRole[]
  photoURL?: string | null
  isDisabled?: boolean
  emailVerified: boolean
  authProvider: AuthProvider
  twoFactorAuth: TwoFactorAuthConfig
  lastLogin?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
  activityLog?: UserActivity[]
}

export interface UserFormData {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  address?: string
  birthday?: Date | null
  roles: UserRole[]
  tags: string[]
  prefs: UserPreferences
}

export interface PasswordUpdateData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface PhotoUploadData {
  file: File
  crop?: {
    x: number
    y: number
    width: number
    height: number
  }
}
