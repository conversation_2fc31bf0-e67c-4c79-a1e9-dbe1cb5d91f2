/**
 * @fileoverview Schema definitions for users collection
 */

/**
 * User document schema in Firestore
 * Collection: users
 * 
 * @example
 * {
 *   email: "<EMAIL>",
 *   firstName: "<PERSON>",
 *   lastName: "<PERSON><PERSON>",
 *   username: "joh<PERSON><PERSON>",
 *   photoURL: "https://example.com/photo.jpg",
 *   phone: "+1234567890",
 *   address: "123 Main St",
 *   birthday: Timestamp,
 *   isFrightened: false,
 *   roles: [
 *     { "artist": { artistId: "abc123" } },
 *     { "bandLeader": { actId: "xyz789" } },
 *     { "admin": {} }
 *   ],
 *   prefs: {
 *     isSubscribed: true,
 *     viewAsGrid: false
 *   },
 *   tags: ["musician", "vocalist"],
 *   createdAt: Timestamp,
 *   updatedAt: Timestamp
 * }
 */

export const userSchema = {
  email: 'string',
  firstName: 'string',
  lastName: 'string',
  username: 'string?',
  photoURL: 'string?',
  phone: 'string?',
  address: 'string?',
  birthday: 'timestamp?',
  isFrightened: 'boolean?',
  roles: [{
    [key: 'string']: {
      artistId: 'string?',  // References artists/{artistId}
      actId: 'string?'      // References acts/{actId}
    }
  }],
  prefs: {
    isSubscribed: 'boolean',
    viewAsGrid: 'boolean'
  },
  tags: ['string'],
  createdAt: 'timestamp',
  updatedAt: 'timestamp'
}

/**
 * Firestore security rules for users collection
 * @example
 * match /users/{userId} {
 *   allow read: if request.auth != null;
 *   allow write: if request.auth.uid == userId;
 *   allow update: if request.auth.uid == userId 
 *     || (request.auth.token.admin == true 
 *     && request.resource.data.diff(resource.data).affectedKeys()
 *       .hasOnly(['roles', 'tags', 'updatedAt']));
 * }
 */
