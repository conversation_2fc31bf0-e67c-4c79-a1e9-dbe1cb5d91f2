import { ref, computed, watch, unref } from 'vue'
import { useUrlSearchParams } from '@vueuse/core'
import { useRouter } from 'vue-router'
import { useDailyTimer } from './useDailyTimer'
import { useEvents } from './useEvents'
import type { ComputedRef } from 'vue'
import type { Timestamp } from 'firebase/firestore'

export type UnavailabilitySpanType = 'single' | 'start' | 'middle' | 'end'

export type UnavailableArtistWithSpan = {
  id: string
  stageName: string
  photoUrl?: string
  initials?: string
  unavailabilityReason: string
  spanType: UnavailabilitySpanType
  spanId: string // Unique identifier for the span group
}

type CalendarEvent = {
  id?: string
  when: Timestamp
  confirmed?: boolean
  [key: string]: any
}

type CalendarDay = {
  date: Date
  isToday: boolean
  month: number
  hasGig: boolean
  hasPastGig: boolean
  hasUpcomingGig: boolean
  gigDetails: CalendarEvent[]
}

const STORAGE_KEY = 'calendar-last-focused-date'
const SELECTED_DATES_KEY = 'calendar-selected-dates'

function getStoredDate(): Date {
  try {
    const storedDate = localStorage.getItem(STORAGE_KEY)
    if (!storedDate) return new Date()

    const date = new Date(storedDate)
    return isNaN(date.getTime()) ? new Date() : date
  } catch (e) {
    console.error('Error retrieving stored date:', e)
    return new Date()
  }
}

function getStoredSelectedDates(): Date[] {
  try {
    const storedDates = localStorage.getItem(SELECTED_DATES_KEY)
    if (!storedDates) return []

    const dates = JSON.parse(storedDates).map(
      (dateStr: string) => new Date(dateStr),
    )
    return dates.every(
      (date: { getTime: () => number }) => !isNaN(date.getTime()),
    )
      ? dates
      : []
  } catch (e) {
    console.error('Error retrieving stored selected dates:', e)
    return []
  }
}

function persistDate(date: Date): void {
  try {
    localStorage.setItem(STORAGE_KEY, date.toISOString())
  } catch (e) {
    console.error('Error persisting date:', e)
  }
}

function persistSelectedDates(dates: Date[]): void {
  try {
    localStorage.setItem(
      SELECTED_DATES_KEY,
      JSON.stringify(dates.map(d => d.toISOString())),
    )
  } catch (e) {
    console.error('Error persisting selected dates:', e)
  }
}

export function useCalendar() {
  const { getEventsForDay, events } = useEvents() // Add events to destructuring
  const router = useRouter()
  const params = useUrlSearchParams('history')

  // State
  const currentDate = ref(getStoredDate())
  const selectedDates = ref<Date[]>(getStoredSelectedDates())
  const lastSelectedDate = ref<Date | null>(
    selectedDates.value[selectedDates.value.length - 1] || null,
  )
  const showExpandedCalendar = ref(false)

  // Setup daily timer for midnight updates
  useDailyTimer(() => {
    const today = new Date()
    if (
      today.getDate() !== currentDate.value.getDate() ||
      today.getMonth() !== currentDate.value.getMonth() ||
      today.getFullYear() !== currentDate.value.getFullYear()
    ) {
      updateCurrentDate(today)
    }
  }, '00:00')

  // Persist date changes to localStorage
  watch(
    () => currentDate.value,
    newDate => {
      if (newDate instanceof Date && !isNaN(newDate.getTime())) {
        persistDate(newDate)
      }
    },
  )

  // Watch for changes to selected dates and persist them
  watch(
    selectedDates,
    newDates => {
      persistSelectedDates(newDates)
    },
    { deep: true },
  )

  // Calendar Grid Computation
  const calendarDays: ComputedRef<CalendarDay[]> = computed(() => {
    const days: CalendarDay[] = []
    const today = new Date() // Use actual today for comparison
    const current = currentDate.value
    let start: Date

    // If navigating to specific month (first day is selected), align to first row
    if (current.getDate() === 1) {
      start = new Date(current.getFullYear(), current.getMonth(), 1)
      // Move to previous Monday
      while (start.getDay() !== 1) {
        start.setDate(start.getDate() - 1)
      }
    } else {
      // For other navigation, center the selected date in the middle row
      start = new Date(current)
      // Move back to find Monday of the middle week's start
      // First, move back to Monday of current week
      while (start.getDay() !== 1) {
        start.setDate(start.getDate() - 1)
      }
      // Then move back 2 more weeks to have selected date in middle week
      start.setDate(start.getDate() - 14)
    }

    // Generate exactly 35 days (5 weeks)
    const end = new Date(start)
    end.setDate(end.getDate() + 34)

    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const currentDate = new Date(d)
      currentDate.setHours(0, 0, 0, 0)

      const dayEvents = getEventsForDay(currentDate)

      days.push({
        date: currentDate,
        isToday: currentDate.toDateString() === today.toDateString(),
        month: currentDate.getMonth(),
        hasGig: dayEvents.length > 0,
        hasPastGig: dayEvents.some(e => new Date(e.when.toDate()) < today),
        hasUpcomingGig: dayEvents.some(e => new Date(e.when.toDate()) >= today),
        gigDetails: dayEvents,
      })
    }

    return days
  })

  const weeks = computed(() => {
    const result: CalendarDay[][] = []
    for (let i = 0; i < calendarDays.value.length; i += 7) {
      const weekDays = calendarDays.value.slice(i, i + 7).map(day => ({
        ...day,
        date: day.date,
        month: day.month,
        gigDetails: getEventsForDay(day.date), // Add this line to include events
      }))
      result.push(weekDays)
    }
    return result
  })

  // Month Navigation
  const allMonths = computed(() => {
    return Array.from({ length: 12 }, (_, i) => {
      const date = new Date(currentDate.value)
      date.setMonth(i)
      return {
        index: i,
        name: date.toLocaleString('default', { month: 'long' }),
      }
    })
  })

  // Event Handling
  function getEventDetails(events: CalendarEvent[] | null) {
    if (!events?.length) return null
    return events.map(event => ({
      ...event,
      formattedTime: new Date(event.when.toDate()).toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
      }),
    }))
  }

  // Navigation Functions
  function updateCurrentDate(date: Date) {
    if (date instanceof Date && !isNaN(date.getTime())) {
      currentDate.value = new Date(date)
      params.date = date.toISOString().split('T')[0]
    }
  }

  function navigateToDay(date: Date) {
    const formattedDate = date.toISOString().split('T')[0]
    updateCurrentDate(date)
    router.push({ query: { date: formattedDate } })
  }

  function navigateToMonth(monthIndex: number, year?: number) {
    const newDate = new Date(currentDate.value)
    if (year) {
      newDate.setFullYear(year)
    }
    newDate.setMonth(monthIndex)
    newDate.setDate(1) // Explicitly set to first day of month
    navigateToDay(newDate)
  }

  function moveBackward() {
    if (hasOlderEvents.value) {
      const newDate = new Date(currentDate.value)
      newDate.setDate(newDate.getDate() - 21) // Move back 3 weeks
      updateCurrentDate(newDate)
    }
  }

  function moveForward() {
    const newDate = new Date(currentDate.value)
    newDate.setDate(newDate.getDate() + 21) // Move forward 3 weeks
    updateCurrentDate(newDate)
  }

  function resetToToday() {
    updateCurrentDate(new Date()) // Today's date will be centered
  }

  function toggleCalendarSize() {
    showExpandedCalendar.value = !showExpandedCalendar.value
  }

  // Computed Properties
  const selectedEvent = computed(() => {
    if (!params.eventId) return null
    return events.value.find(event => event.id === params.eventId) || null
  })

  const hasOlderEvents = computed(() => {
    if (!events?.value?.length) return false
    const oldestEventDate = Math.min(
      ...events.value.map((e: CalendarEvent) => {
        const eventDate = e.when
        return eventDate.toDate().getTime()
      }),
    )
    const startDate = new Date(currentDate.value)
    startDate.setDate(1)
    return oldestEventDate < startDate.getTime()
  })

  // Find the first month that has its 1st day visible in the grid
  const visibleMonth = computed(() => {
    if (!weeks.value?.length) return currentDate.value.getMonth()

    for (const week of weeks.value) {
      for (const day of Object.values(week)) {
        if (day.date.getDate() === 1) {
          return day.date.getMonth()
        }
      }
    }
    return currentDate.value.getMonth()
  })

  // Create a date object for the visible month
  const visibleMonthDate = computed(
    () => new Date(currentDate.value.getFullYear(), visibleMonth.value),
  )

  // Helper function to check if two dates are the same day
  function isSameDay(date1: Date, date2: Date): boolean {
    return date1.toDateString() === date2.toDateString()
  }

  // Helper function to get dates between two dates
  function getDatesBetween(startDate: Date, endDate: Date): Date[] {
    const dates: Date[] = []
    let currentDate = new Date(startDate)
    let lastDate = new Date(endDate)

    // Ensure we're working with dates at midnight
    currentDate.setHours(0, 0, 0, 0)
    lastDate.setHours(0, 0, 0, 0)

    // Determine direction and swap if needed
    const direction = currentDate <= lastDate ? 1 : -1
    if (direction === -1) {
      ;[currentDate, lastDate] = [lastDate, currentDate]
    }

    // Generate dates
    while (currentDate <= lastDate) {
      dates.push(new Date(currentDate))
      currentDate.setDate(currentDate.getDate() + 1)
    }

    // Return in correct order based on direction
    return direction === 1 ? dates : dates.reverse()
  }

  // Helper function to analyze consecutive unavailable dates and determine span types
  function analyzeUnavailabilitySpans(
    unavailableDates: any[],
    artists: any[],
    targetDate: Date,
  ): UnavailableArtistWithSpan[] {
    if (!unavailableDates?.length || !artists?.length) return []

    const checkDate = new Date(targetDate)
    checkDate.setHours(12, 0, 0, 0)

    // Get unavailabilities for this date
    const dayUnavailabilities = unavailableDates.filter(unavailable => {
      if (!unavailable.startDate || !unavailable.endDate) return false
      const start = new Date(unavailable.startDate)
      const end = new Date(unavailable.endDate)
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)

      return checkDate >= start && checkDate <= end
    })

    const result: UnavailableArtistWithSpan[] = []

    for (const unavailable of dayUnavailabilities) {
      const artist = artists.find(a => a.id === unavailable.artistId)
      if (!artist || !artist.id) continue

      // Determine span type
      const startDate = new Date(unavailable.startDate)
      const endDate = new Date(unavailable.endDate)
      startDate.setHours(0, 0, 0, 0)
      endDate.setHours(0, 0, 0, 0)

      const isStart = checkDate.getTime() === startDate.getTime()
      const isEnd = checkDate.getTime() === endDate.getTime()
      const isSingleDay = startDate.getTime() === endDate.getTime()

      let spanType: UnavailabilitySpanType
      if (isSingleDay) {
        spanType = 'single'
      } else if (isStart) {
        spanType = 'start'
      } else if (isEnd) {
        spanType = 'end'
      } else {
        spanType = 'middle'
      }

      result.push({
        id: artist.id,
        stageName: artist.stageName,
        photoUrl: artist.photoUrl,
        initials: artist.initials,
        unavailabilityReason: unavailable.reason || 'Unavailable',
        spanType,
        spanId: `${artist.id}-${unavailable.id}`, // Unique identifier for this span
      })
    }

    return result
  }

  // Function to handle date selection
  function selectDate(date: Date, event?: MouseEvent): void {
    const newDate = new Date(date)
    newDate.setHours(0, 0, 0, 0) // Normalize to midnight

    const isDateSelected = selectedDates.value.some(d => isSameDay(d, newDate))

    if (event?.shiftKey && lastSelectedDate.value) {
      // Shift+click: toggle selection of all dates between last selected and current
      const dateRange = getDatesBetween(lastSelectedDate.value, newDate)

      if (isDateSelected) {
        // If clicked date is selected, deselect the range
        selectedDates.value = selectedDates.value.filter(
          d => !dateRange.some(rangeDate => isSameDay(rangeDate, d)),
        )
      } else {
        // If clicked date is not selected, select the range
        const newDates = [...selectedDates.value]
        dateRange.forEach(rangeDate => {
          if (!newDates.some(d => isSameDay(d, rangeDate))) {
            newDates.push(rangeDate)
          }
        })
        selectedDates.value = newDates
      }
    } else if (event?.ctrlKey || event?.metaKey) {
      // Ctrl/Cmd+click: toggle selection of the date
      if (isDateSelected) {
        selectedDates.value = selectedDates.value.filter(
          d => !isSameDay(d, newDate),
        )
      } else {
        selectedDates.value.push(newDate)
      }
    } else {
      // Normal click: if date is selected, clear all selections; if not, select only this date
      if (isDateSelected) {
        selectedDates.value = []
      } else {
        selectedDates.value = [newDate]
      }
    }

    lastSelectedDate.value = newDate
    persistSelectedDates(selectedDates.value)
  }

  return {
    // State
    currentDate,
    selectedDates,
    showExpandedCalendar,
    selectedEvent,
    hasOlderEvents,
    visibleMonth,
    visibleMonthDate,

    // Grid Data
    calendarDays,
    weeks,
    allMonths,

    // Event Helpers
    getEventsForDay,
    getEventDetails,

    // Navigation
    updateCurrentDate,
    navigateToDay,
    navigateToMonth,
    moveBackward,
    moveForward,
    resetToToday,
    toggleCalendarSize,
    selectDate,
    isSameDay,
    events, // Also return events if needed by consumers

    // Unavailability helpers
    analyzeUnavailabilitySpans,
  }
}
