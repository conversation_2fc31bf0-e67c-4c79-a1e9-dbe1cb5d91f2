import { createRouter, createWebHistory } from 'vue-router'
import { useFirebase } from '../composables/useFirebase'
import SetListBuilder from '@/views/SetListBuilder.vue'
import { navigationService } from '../services/navigation'
import VenueDetails from '@/views/venues/VenueDetails.vue'
import type { User } from 'firebase/auth'
const { auth } = useFirebase()

// Interface for extended User type with isAdmin property
type ExtendedUser = User & {
  isAdmin?: boolean
}

// First, let's modify the getCurrentUser helper to include admin check
const getCurrentUser = (): Promise<ExtendedUser | null> => {
  return new Promise((resolve, reject) => {
    const unsubscribe = auth.onAuthStateChanged((user: User | null) => {
      unsubscribe()
      if (user) {
        // Check if user has admin claim
        user
          .getIdTokenResult()
          .then(idTokenResult => {
            const extendedUser = user as ExtendedUser
            extendedUser.isAdmin = idTokenResult.claims.admin === true
            resolve(extendedUser)
          })
          .catch(reject)
      } else {
        resolve(null)
      }
    }, reject)
  })
}

import Home from '../views/Home.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home,
      meta: {
        requiresAuth: true,
        title: 'Dave Collison Entertainment',
      },
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/Dashboard.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Dave Collison Entertainment',
      },
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/Login.vue'),
      meta: {
        requiresGuest: true,
        title: 'Login',
      },
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/Admin.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Admin',
      },
    },
    {
      path: '/events',
      name: 'events.index',
      component: () => import('../views/events/index.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Events',
      },
    },
    {
      path: '/events/payments',
      name: 'events.payments',
      component: () => import('../views/events/payments.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Manage Payments',
      },
    },
    {
      path: '/events/dev',
      name: 'events.dev',
      component: () => import('../views/events/dev.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Events Dev View',
      },
    },
    {
      path: '/events/create',
      name: 'events.create',
      component: () => import('../views/events/edit.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Create Event',
      },
    },
    {
      path: '/events/:id?/edit',
      name: 'events.edit',
      component: () => import('../views/events/edit.vue'),
      props: true,
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Edit Event',
      },
    },
    {
      path: '/events/:id',
      name: 'events.show',
      component: () => import('../views/events/show.vue'),
      props: true,
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Event Details',
      },
    },
    {
      path: '/events/day/:date',
      name: 'events.day',
      component: () => import('../views/events/DayEvents.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Day Events',
      },
    },
    {
      path: '/venues',
      name: 'venues.index',
      component: () => import('../views/venues/index.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Venues',
      },
    },
    {
      path: '/venues/create',
      name: 'venues.create',
      component: () => import('../views/venues/create.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Create Venue',
      },
    },
    {
      path: '/venues/:id',
      name: 'venues.show',
      component: () => import('../views/venues/show.vue'),
      props: true,
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Venue Details',
      },
    },
    {
      path: '/venues/:id/edit',
      name: 'venues.edit',
      component: () => import('../views/venues/edit.vue'),
      props: true,
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Edit Venue',
      },
    },
    {
      path: '/songs',
      name: 'songs.index',
      component: () => import('../views/songs/index.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Songs',
      },
    },
    {
      path: '/artists',
      name: 'artists.index',
      component: () => import('../views/artists/index.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Artists',
      },
    },
    {
      path: '/artists/create',
      name: 'artists.create',
      component: () => import('../views/artists/create.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Create Artist',
      },
    },
    {
      path: '/artists/:id',
      name: 'artists.show',
      component: () => import('../views/artists/artist.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Artist Details',
      },
    },
    {
      path: '/artists/:id/edit',
      name: 'artists.edit',
      component: () => import('../views/artists/create.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Edit Artist',
      },
    },
    {
      path: '/acts',
      name: 'acts.index',
      component: () => import('../views/acts/index.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Acts',
      },
    },
    {
      path: '/acts/create',
      name: 'acts.create',
      component: () => import('../views/acts/create.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Create Act',
      },
    },
    {
      path: '/acts/:id',
      name: 'acts.show',
      component: () => import('../views/acts/show.vue'),
      props: true,
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Act Details',
      },
    },
    {
      path: '/acts/:id/edit',
      name: 'acts.edit',
      component: () => import('@/views/acts/edit.vue'),
      props: true,
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Edit Act',
      },
    },
    {
      path: '/calendar',
      name: 'calendar',
      component: () => import('../views/Calendar.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Calendar',
      },
    },
    {
      path: '/full-calendar',
      name: 'full-calendar',
      component: () => import('../views/FullCalendar.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Full Calendar',
      },
    },
    {
      path: '/setlist-builder',
      name: 'setlist-builder',
      component: SetListBuilder,
    },
    {
      path: '/admin/schema-analyzer',
      name: 'schema-analyzer',
      component: () => import('../components/SchemaAnalyzer.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
      },
    },
    {
      path: '/venues/:name',
      name: 'venue-details',
      component: VenueDetails,
    },
    {
      path: '/tasks',
      name: 'tasks',
      component: () => import('../views/tasks/index.vue'),
      meta: {
        requiresAuth: true,
        title: 'Tasks',
      },
    },
    {
      path: '/tasks/create',
      name: 'tasks.create',
      component: () => import('../views/tasks/index.vue'),
      meta: {
        requiresAuth: true,
        title: 'Create Task',
      },
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/profile/index.vue'),
      meta: {
        requiresAuth: true,
        title: 'Profile',
      },
    },
    {
      path: '/admin/users',
      name: 'users',
      component: () => import('@/views/admin/Users.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
      },
    },
    {
      path: '/admin/roles',
      name: 'user-roles',
      component: () => import('@/views/admin/UserRoles.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
      },
    },
    {
      path: '/admin/users/:id',
      name: 'user-details',
      component: () => import('@/views/admin/UserDetails.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/admin/users/:id/edit',
      name: 'edit-user',
      component: () => import('../views/admin/EditUser.vue'),
      props: true,
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        title: 'Edit User',
      },
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/404.vue'),
      meta: {
        requiresRedirect: true,
        title: 'Not Found',
      },
    },
  ],
})

// Set the router in our navigation service
navigationService.setRouter(router)

// Navigation guard
router.beforeEach(async (to, from, next) => {
  const user = await getCurrentUser()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)

  // Update page title
  document.title = (to.meta.title as string) || 'Dave Collison Entertainment'

  if (requiresAuth && !user) {
    // If route requires auth and user isn't authenticated
    console.log('Route requires auth, redirecting to login')
    next('/login')
  } else if (requiresGuest && user) {
    // If route requires guest and user is authenticated
    console.log('Route requires guest, redirecting to dashboard')
    next('/')
  } else if (requiresAdmin && (!user || !user.isAdmin)) {
    // If route requires admin and user isn't admin
    console.log('Route requires admin, redirecting to dashboard')
    next('/')
  } else {
    next()
  }
})

export default router
