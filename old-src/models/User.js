/**
 * @typedef {Object} RoleData
 * @property {string} [actId] - ID of the act/band if role is 'bandLeader'
 * @property {string} [artistId] - ID of the artist if role is 'artist'
 */

/**
 * @typedef {Object.<string, RoleData>} UserRole - Key is the role name (e.g., 'artist', 'bandLeader')
 */

/**
 * @typedef {Object} UserPreferences
 * @property {boolean} isSubscribed - Whether the user is subscribed to notifications
 * @property {boolean} viewAsGrid - Whether the user prefers grid view
 */

/**
 * @typedef {Object} FirestoreTimestamp
 * @property {number} seconds - Seconds since epoch
 * @property {number} nanoseconds - Nanoseconds part of the timestamp
 */

/**
 * @typedef {Object} UserData
 * @property {string} [id] - User's ID
 * @property {string} email - User's email address
 * @property {string} firstName - User's first name
 * @property {string} lastName - User's last name
 * @property {string} username - User's username
 * @property {UserPreferences} prefs - User preferences
 * @property {string[]} tags - User's associated tags
 * @property {string} [phone] - User's phone number
 * @property {FirestoreTimestamp} [birthday] - User's birthday as Firestore timestamp
 * @property {string} [address] - User's address
 * @property {boolean} [isFrightened] - Whether user is frightened
 * @property {UserRole[]} [roles] - User's roles with associated data
 * @property {string} [photoURL] - User's photo URL
 */

/**
 * Class representing a User in the system
 */
export class User {
  /**
   * Create a new User instance
   * @param {UserData} data - The user data
   */
  constructor(data) {
    this.id = data.id
    this.email = data.email
    this.firstName = data.firstName
    this.lastName = data.lastName
    this.username = data.username
    this.prefs = {
      isSubscribed: data.prefs?.isSubscribed ?? false,
      viewAsGrid: data.prefs?.viewAsGrid ?? false
    }
    this.tags = data.tags || []
    this.phone = data.phone || ''
    this.birthday = data.birthday || null
    this.address = data.address || ''
    this.isFrightened = data.isFrightened || false
    this.roles = data.roles || []
    this.photoURL = data.photoURL || null
  }

  /**
   * Get user's full name
   * @returns {string} The full name of the user
   */
  get fullName() {
    return `${this.firstName} ${this.lastName}`.trim()
  }

  /**
   * Get user's display name (username or full name)
   * @returns {string} The display name to use
   */
  get displayName() {
    return this.username || this.fullName
  }

  /**
   * Get formatted role names for display
   * @returns {string} Comma-separated list of role names
   */
  get roleNames() {
    if (!this.roles?.length) return ''

    return this.roles
      .map(role => {
        const roleName = Object.keys(role)[0]
        if (!roleName) return ''

        switch (roleName.toLowerCase()) {
          case 'artist':
            return 'Artist'
          case 'bandleader':
            return 'Band Leader'
          case 'admin':
            return 'Administrator'
          default:
            return roleName.charAt(0).toUpperCase() + roleName.slice(1)
        }
      })
      .filter(Boolean)
      .join(', ')
  }

  /**
   * Check if user has a specific role
   * @param {string} roleName - The role name to check for
   * @returns {boolean} True if user has the role, false otherwise
   */
  hasRole(roleName) {
    return this.roles.some(role => Object.keys(role)[0]?.toLowerCase() === roleName.toLowerCase())
  }

  /**
   * Get role data for a specific role
   * @param {string} roleName - The role name to get data for
   * @returns {RoleData|null} The role data or null if role not found
   */
  getRoleData(roleName) {
    const role = this.roles.find(r => Object.keys(r)[0]?.toLowerCase() === roleName.toLowerCase())
    return role ? Object.values(role)[0] : null
  }

  /**
   * Get user's avatar URL
   * Note: Artist's avatar should be fetched from the artist document directly
   * @returns {string|null} The avatar URL to use
   */
  get avatarURL() {
    return this.photoURL || null
  }

  /**
   * Get user's subscription status
   * @returns {boolean} Whether the user is subscribed to notifications
   */
  get isSubscribed() {
    return this.prefs?.isSubscribed || false
  }

  /**
   * Convert the user instance to a plain object for Firestore
   * @returns {Object} Plain object representation of the user
   */
  toFirestore() {
    return {
      email: this.email,
      firstName: this.firstName,
      lastName: this.lastName,
      username: this.username,
      prefs: this.prefs,
      tags: this.tags,
      phone: this.phone,
      birthday: this.birthday,
      address: this.address,
      isFrightened: this.isFrightened,
      roles: this.roles,
      photoURL: this.photoURL
    }
  }

  /**
   * Create a User instance from Firestore data
   * @param {UserData} data - The Firestore document data
   * @returns {User} A new User instance
   */
  static fromFirestore(data) {
    return new User(data)
  }
}
