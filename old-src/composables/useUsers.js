import { ref, computed } from 'vue'
import { collection, doc, getDoc, getDocs, query, where, updateDoc, serverTimestamp, addDoc, deleteDoc } from 'firebase/firestore'
import { useFirebase } from './useFirebase'
import { User } from '@/models/User'

export function useUsers() {
  const { db, auth } = useFirebase()
  const users = ref([])
  const loading = ref(false)
  const error = ref(null)
  let unsubscribe = null

  // Sort users by name
  const sortedUsers = computed(() => {
    return [...users.value].sort((a, b) => a.fullName.localeCompare(b.fullName))
  })

  // Filter active users
  const activeUsers = computed(() => {
    return sortedUsers.value.filter(user => !user.isDisabled)
  })

  // Check if email exists in subscribers collection
  async function checkSubscriberStatus(email) {
    try {
      const q = query(collection(db, 'subscribers'), where('email', '==', email))
      const snapshot = await getDocs(q)
      return !snapshot.empty
    } catch (err) {
      error.value = err.message
      return false
    }
  }

  // Get a single user by ID
  async function getUser(userId) {
    try {
      const userDoc = await getDoc(doc(db, 'users', userId))
      if (!userDoc.exists()) return null

      const userData = userDoc.data()
      userData.id = userDoc.id

      // If this is the current user, sync photoURL from Firebase Auth
      if (userId === auth.currentUser?.uid) {
        userData.photoURL = auth.currentUser.photoURL || userData.photoURL
      }

      // Check subscriber status
      const isSubscribed = await checkSubscriberStatus(userData.email)

      // Update user preferences if subscription status is different
      if (isSubscribed !== userData.prefs?.isSubscribed) {
        const userRef = doc(db, 'users', userId)
        await updateDoc(userRef, {
          'prefs.isSubscribed': isSubscribed,
          updatedAt: serverTimestamp()
        })
        userData.prefs = {
          ...userData.prefs,
          isSubscribed
        }
      }

      // Get admin claim from Firebase Auth token
      const token = await auth.currentUser?.getIdTokenResult()
      const isAdmin = token?.claims?.admin || false

      // If this is the current user, merge admin status
      if (userId === auth.currentUser?.uid) {
        userData.roles = userData.roles || []
        if (isAdmin) {
          // Remove any existing admin role first
          userData.roles = userData.roles.filter(role => !Object.keys(role).includes('admin'))
          // Add the admin role in the correct format
          userData.roles.push({ 'admin': {} })
        }
      }

      return User.fromFirestore(userData)
    } catch (err) {
      error.value = err.message
      return null
    }
  }

  // Subscribe to users collection
  async function subscribeToUsers(filterOptions = {}) {
    loading.value = true
    error.value = null

    try {
      const constraints = []
      if (filterOptions.role) {
        constraints.push(where('roles', 'array-contains', { type: filterOptions.role }))
      }

      const q = constraints.length > 0
        ? query(collection(db, 'users'), ...constraints)
        : collection(db, 'users')

      const querySnapshot = await getDocs(q)
      users.value = await Promise.all(querySnapshot.docs.map(async doc => {
        const data = doc.data()
        data.id = doc.id

        // Check subscriber status for each user
        data.prefs = data.prefs || {}
        data.prefs.isSubscribed = await checkSubscriberStatus(data.email)

        return User.fromFirestore(data)
      }))
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  // Upsert a user with role data
  async function upsertUser(userData, roleData = null) {
    try {
      const userRef = doc(db, 'users', userData.id)
      const data = {
        ...userData,
        updatedAt: serverTimestamp()
      }

      if (roleData) {
        data.roles = userData.roles || []
        const existingRoleIndex = data.roles.findIndex(r => r.type === roleData.type)

        if (existingRoleIndex >= 0) {
          data.roles[existingRoleIndex].data = roleData.data
        } else {
          data.roles.push({ type: roleData.type, data: roleData.data })
        }
      }

      await updateDoc(userRef, data)
      return true
    } catch (err) {
      error.value = err.message
      return false
    }
  }

  // Update user preferences
  async function updateUserPreferences(userId, prefs) {
    try {
      const userRef = doc(db, 'users', userId)

      // If updating subscription status, sync with subscribers collection
      if (prefs.hasOwnProperty('isSubscribed')) {
        const userData = (await getDoc(userRef)).data()
        if (prefs.isSubscribed) {
          // Add to subscribers if not exists
          const subscribersRef = collection(db, 'subscribers')
          const q = query(subscribersRef, where('email', '==', userData.email))
          const snapshot = await getDocs(q)

          if (snapshot.empty) {
            await addDoc(subscribersRef, {
              email: userData.email,
              name: userData.firstName + ' ' + userData.lastName,
              createdAt: serverTimestamp()
            })
          }
        } else {
          // Remove from subscribers if exists
          const q = query(collection(db, 'subscribers'), where('email', '==', userData.email))
          const snapshot = await getDocs(q)

          if (!snapshot.empty) {
            await deleteDoc(snapshot.docs[0].ref)
          }
        }
      }

      await updateDoc(userRef, {
        prefs,
        updatedAt: serverTimestamp()
      })
      return true
    } catch (err) {
      error.value = err.message
      return false
    }
  }

  // Update user profile
  async function updateUser(userData) {
    if (!userData?.id) throw new Error('User ID is required')

    try {
      const userRef = doc(db, 'users', userData.id)
      await updateDoc(userRef, {
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone || null,
        updatedAt: serverTimestamp()
      })
      return true
    } catch (err) {
      throw new Error(err.message || 'Failed to update profile')
    }
  }

  // Get current user's data
  async function getCurrentUserData() {
    const user = auth.currentUser
    if (!user?.uid) return null
    return getUser(user.uid)
  }

  // Cleanup function
  function cleanup() {
    if (unsubscribe) unsubscribe()
  }

  return {
    users: sortedUsers,
    activeUsers,
    loading,
    error,
    subscribeToUsers,
    getUser,
    upsertUser,
    updateUserPreferences,
    getCurrentUserData,
    updateUser,
    cleanup
  }
}
