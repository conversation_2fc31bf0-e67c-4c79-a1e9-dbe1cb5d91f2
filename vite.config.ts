import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'

// Custom JSON plugin
function jsonPlugin() {
  return {
    name: 'vite:json',
    transform(code: string, id: string) {
      if (id.endsWith('.json')) {
        // Check if the code already has an export statement
        if (!code.includes('export default')) {
          return {
            code: `export default ${code}`,
            map: null,
          }
        }
      }
    },
  }
}

export default defineConfig({
  plugins: [vue(), vueJsx(), jsonPlugin()],
  resolve: {
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})
